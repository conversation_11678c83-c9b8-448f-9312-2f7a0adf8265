import axios from 'axios';
import { searchCNFFoods, getCNFFoodById, formatCNFFood } from './cnfDatabase';

// Open Food Facts API base URL
const API_BASE_URL = 'https://world.openfoodfacts.org/api/v0';

/**
 * Get product information by barcode
 * @param {string} barcode - EAN/UPC barcode
 * @returns {Promise} - Promise with product data
 */
export const getProductByBarcode = async (barcode) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/product/${barcode}.json`);

    if (response.data.status === 1) {
      return {
        success: true,
        data: response.data.product
      };
    } else {
      return {
        success: false,
        error: 'Product not found'
      };
    }
  } catch (error) {
    console.error('Error fetching product data:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch product data'
    };
  }
};

/**
 * Search for products by name
 * @param {string} query - Search query
 * @param {number} page - Page number (default: 1)
 * @param {number} pageSize - Number of results per page (default: 10)
 * @returns {Promise} - Promise with search results
 */
export const searchProducts = async (query, page = 1, pageSize = 10) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/search`, {
      params: {
        search_terms: query,
        page,
        page_size: pageSize,
        json: true
      }
    });

    if (response.data.products && response.data.products.length > 0) {
      return {
        success: true,
        data: response.data.products,
        count: response.data.count,
        page: response.data.page,
        pageSize: response.data.page_size
      };
    } else {
      return {
        success: false,
        error: 'No products found'
      };
    }
  } catch (error) {
    console.error('Error searching products:', error);
    return {
      success: false,
      error: error.message || 'Failed to search products'
    };
  }
};

/**
 * Search across multiple food databases
 * @param {string} query - Search query
 * @param {Array} sources - Array of sources to search ['openfoodfacts', 'cnf']
 * @param {number} limit - Maximum results per source
 * @returns {Promise} - Promise with combined search results
 */
export const searchMultipleDatabases = async (query, sources = ['openfoodfacts', 'cnf'], limit = 5) => {
  try {
    const results = [];
    const errors = [];

    // Search Open Food Facts
    if (sources.includes('openfoodfacts')) {
      try {
        const offResult = await searchProducts(query, 1, limit);
        if (offResult.success) {
          results.push({
            source: 'Open Food Facts',
            data: offResult.data.map(product => ({
              ...product,
              source: 'openfoodfacts',
              formatted_name: product.product_name || product.product_name_en || 'Unknown Product'
            }))
          });
        }
      } catch (error) {
        errors.push({ source: 'Open Food Facts', error: error.message });
      }
    }

    // Search Canadian Nutrient File
    if (sources.includes('cnf')) {
      try {
        const cnfResult = await searchCNFFoods(query, limit);
        if (cnfResult.success) {
          results.push({
            source: 'Canadian Nutrient File',
            data: cnfResult.data.map(food => ({
              ...formatCNFFood(food),
              source: 'cnf',
              formatted_name: food.FoodDescription || food.food_description || 'Unknown Food'
            }))
          });
        }
      } catch (error) {
        errors.push({ source: 'Canadian Nutrient File', error: error.message });
      }
    }

    return {
      success: results.length > 0,
      results: results,
      errors: errors,
      total_sources: sources.length,
      successful_sources: results.length
    };

  } catch (error) {
    console.error('Error searching multiple databases:', error);
    return {
      success: false,
      error: error.message || 'Failed to search databases',
      results: [],
      errors: [{ source: 'General', error: error.message }]
    };
  }
};

/**
 * Get food details from multiple sources
 * @param {string} foodId - Food ID
 * @param {string} source - Source database ('openfoodfacts', 'cnf')
 * @returns {Promise} - Promise with food details
 */
export const getFoodFromSource = async (foodId, source) => {
  try {
    switch (source) {
      case 'openfoodfacts':
        return await getProductByBarcode(foodId);
      case 'cnf':
        return await getCNFFoodById(foodId);
      default:
        return {
          success: false,
          error: 'Unknown food source'
        };
    }
  } catch (error) {
    console.error(`Error getting food from ${source}:`, error);
    return {
      success: false,
      error: error.message || `Failed to get food from ${source}`
    };
  }
};

export default {
  getProductByBarcode,
  searchProducts,
  searchMultipleDatabases,
  getFoodFromSource
};
