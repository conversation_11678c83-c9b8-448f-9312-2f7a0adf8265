/**
 * Test setup file for <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Sets up the testing environment
 */

import { SQLite } from 'expo-sqlite';
import * as FileSystem from 'expo-file-system';

// Mock expo-sqlite
jest.mock('expo-sqlite', () => {
  const mockSQLite = {
    openDatabase: jest.fn().mockReturnValue({
      transaction: jest.fn(),
      exec: jest.fn(),
      _db: {
        close: jest.fn()
      }
    })
  };
  return { SQLite: mockSQLite };
});

// Mock expo-file-system
jest.mock('expo-file-system', () => ({
  documentDirectory: 'file:///mock-directory/',
  makeDirectoryAsync: jest.fn(),
  getInfoAsync: jest.fn().mockResolvedValue({ exists: true }),
  copyAsync: jest.fn(),
  deleteAsync: jest.fn(),
  readAsStringAsync: jest.fn(),
  writeAsStringAsync: jest.fn()
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn().mockResolvedValue(null),
  setItem: jest.fn().mockResolvedValue(),
  removeItem: jest.fn().mockResolvedValue(),
  clear: jest.fn().mockResolvedValue(),
  getAllKeys: jest.fn().mockResolvedValue([]),
  multiGet: jest.fn().mockResolvedValue([]),
  multiSet: jest.fn().mockResolvedValue(),
  multiRemove: jest.fn().mockResolvedValue()
}));



// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid')
}));

// Mock database transaction
global.mockTransaction = (callback) => {
  const tx = {
    executeSql: jest.fn((query, params, success, error) => {
      // Mock successful query with empty result
      success(tx, {
        rows: {
          length: 0,
          item: jest.fn(),
          _array: []
        },
        rowsAffected: 1,
        insertId: 1
      });
    })
  };
  callback(tx);
  return Promise.resolve();
};

// Mock database query result
global.mockQueryResult = (items = []) => {
  return {
    rows: {
      length: items.length,
      item: (index) => items[index],
      _array: items
    },
    rowsAffected: items.length,
    insertId: items.length > 0 ? 1 : null
  };
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();

  // Reset FileSystem mocks
  const FileSystem = require('expo-file-system');
  FileSystem.getInfoAsync.mockResolvedValue({ exists: true });
  FileSystem.readAsStringAsync.mockResolvedValue('');
  FileSystem.writeAsStringAsync.mockResolvedValue();
});
