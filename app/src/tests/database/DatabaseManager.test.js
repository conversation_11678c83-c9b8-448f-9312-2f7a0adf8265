/**
 * Tests for DatabaseManager
 */

import DatabaseManager from '../../database/DatabaseManager';

// Mock expo-sqlite
const mockOpenDatabase = jest.fn();
jest.mock('expo-sqlite', () => ({
  openDatabase: mockOpenDatabase
}));

// Mock expo-file-system for SQL file reading
jest.mock('expo-file-system', () => ({
  readAsStringAsync: jest.fn()
}));

// Mock expo-asset
jest.mock('expo-asset', () => ({
  Asset: {
    fromModule: jest.fn().mockReturnValue({
      downloadAsync: jest.fn().mockResolvedValue(),
      localUri: 'mock://schema.sql'
    })
  }
}));

// Mock react-native Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios'
  }
}));

describe('DatabaseManager', () => {
  let dbManager;

  beforeEach(() => {
    dbManager = DatabaseManager;
    // Reset the singleton state
    dbManager.db = null;
    dbManager.initialized = false;
  });

  afterEach(() => {
    if (dbManager.db) {
      dbManager.close();
    }
  });

  test('should initialize the database', async () => {
    // Mock the transaction implementation
    const mockDb = {
      transaction: jest.fn().mockImplementation((callback, errorCallback, successCallback) => {
        const tx = {
          executeSql: jest.fn((query, params, success) => {
            // Mock the result for _checkIfInitialized
            if (query.includes('SELECT name FROM sqlite_master')) {
              success(tx, { rows: { length: 0 } });
            }
            // Mock the result for executeScript
            else {
              success(tx, {});
            }
          })
        };
        try {
          callback(tx);
          if (successCallback) successCallback();
        } catch (error) {
          if (errorCallback) errorCallback(error);
        }
      }),
      _db: { close: jest.fn() }
    };

    mockOpenDatabase.mockReturnValue(mockDb);

    // Mock the readAsStringAsync to return a simple SQL script
    const mockSql = 'CREATE TABLE IF NOT EXISTS User (id TEXT PRIMARY KEY);';
    require('expo-file-system').readAsStringAsync.mockResolvedValue(mockSql);

    await dbManager.init();

    expect(mockOpenDatabase).toHaveBeenCalledWith('znunizaehler.db');
    expect(dbManager.initialized).toBe(true);
    expect(mockDb.transaction).toHaveBeenCalled();
  });

  test('should execute a query', async () => {
    // Mock the transaction implementation
    const mockDb = {
      transaction: jest.fn().mockImplementation((callback) => {
        const tx = {
          executeSql: jest.fn((query, params, success) => {
            success(tx, {
              rows: {
                length: 1,
                item: (index) => ({ id: 'test-id', name: 'Test' }),
                _array: [{ id: 'test-id', name: 'Test' }]
              },
              rowsAffected: 1,
              insertId: 1
            });
          })
        };
        callback(tx);
        return Promise.resolve();
      }),
      _db: { close: jest.fn() }
    };

    mockOpenDatabase.mockReturnValue(mockDb);
    dbManager.db = mockDb;
    dbManager.initialized = true;

    const result = await dbManager.executeQuery('SELECT * FROM User WHERE id = ?', ['test-id']);

    expect(mockDb.transaction).toHaveBeenCalled();
    expect(result.rows.length).toBe(1);
    expect(result.rows.item(0)).toEqual({ id: 'test-id', name: 'Test' });
  });

  test('should execute a transaction', async () => {
    // Mock the transaction implementation
    const mockDb = {
      transaction: jest.fn().mockImplementation((callback, errorCallback, successCallback) => {
        const tx = {
          executeSql: jest.fn((query, params, success) => {
            success(tx, {
              rows: { length: 0 },
              rowsAffected: 1
            });
          })
        };
        callback(tx);
        if (successCallback) successCallback();
        return Promise.resolve();
      }),
      _db: { close: jest.fn() }
    };

    mockOpenDatabase.mockReturnValue(mockDb);
    dbManager.db = mockDb;
    dbManager.initialized = true;

    const queries = [
      { sql: 'INSERT INTO User (id, name) VALUES (?, ?)', params: ['user1', 'User 1'] },
      { sql: 'INSERT INTO User (id, name) VALUES (?, ?)', params: ['user2', 'User 2'] }
    ];

    await dbManager.executeTransaction(queries);

    expect(mockDb.transaction).toHaveBeenCalled();
    expect(mockDb.transaction.mock.calls[0][0]).toBeInstanceOf(Function);
  });

  test('should check if database is initialized', async () => {
    // Mock the transaction implementation for initialized database
    const mockDb = {
      transaction: jest.fn().mockImplementation((callback) => {
        const tx = {
          executeSql: jest.fn((query, params, success) => {
            success(tx, { rows: { length: 1 } });
          })
        };
        callback(tx);
        return Promise.resolve();
      }),
      _db: { close: jest.fn() }
    };

    mockOpenDatabase.mockReturnValue(mockDb);
    dbManager.db = mockDb;

    const result = await dbManager._checkIfInitialized();

    expect(mockDb.transaction).toHaveBeenCalled();
    expect(result).toBe(true);
  });

  test('should close the database', () => {
    const mockDb = {
      _db: { close: jest.fn() }
    };

    dbManager.db = mockDb;
    dbManager.initialized = true;

    dbManager.close();

    expect(mockDb._db.close).toHaveBeenCalled();
    expect(dbManager.db).toBeNull();
    expect(dbManager.initialized).toBe(false);
  });
});
