/**
 * Tests for Consumption model
 */

import Consumption from '../../models/Consumption';
import Food from '../../models/Food';
import dbManager from '../../database/DatabaseManager';

// Mock the database manager
jest.mock('../../database/DatabaseManager', () => ({
  init: jest.fn().mockResolvedValue(),
  executeQuery: jest.fn(),
  update: jest.fn(),
  softDelete: jest.fn(),
  executeTransaction: jest.fn()
}));

// Mock the Food model
const mockFood = {
  id: 'food1',
  name: 'Test Food',
  loadNutrients: jest.fn().mockResolvedValue(),
  nutrients: [
    { nutrient_id: 'nutrient-calories', name: 'Calories', amount: 100, unit: 'kcal', is_macro: 1 },
    { nutrient_id: 'nutrient-protein', name: 'Protein', amount: 10, unit: 'g', is_macro: 1 },
    { nutrient_id: 'nutrient-carbs', name: 'Carbohydrates', amount: 20, unit: 'g', is_macro: 1 },
    { nutrient_id: 'nutrient-fat', name: 'Fat', amount: 5, unit: 'g', is_macro: 1 }
  ]
};

jest.mock('../../models/Food', () => {
  const mockFoodClass = jest.fn().mockImplementation(() => mockFood);
  mockFoodClass.findById = jest.fn().mockResolvedValue(mockFood);
  return {
    __esModule: true,
    default: mockFoodClass
  };
});

describe('Consumption Model', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the Food mock
    const Food = require('../../models/Food').default;
    Food.findById.mockResolvedValue(mockFood);
  });

  test('should create a new Consumption instance', () => {
    const today = new Date().toISOString().split('T')[0];
    const consumption = new Consumption({
      id: 'consumption1',
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'breakfast',
      notes: 'Test notes'
    });

    expect(consumption.id).toBe('consumption1');
    expect(consumption.user_id).toBe('user1');
    expect(consumption.consumption_date).toBe('2023-01-01');
    expect(consumption.meal_type).toBe('breakfast');
    expect(consumption.notes).toBe('Test notes');
    expect(consumption.items).toEqual([]);

    // Test default values
    const defaultConsumption = new Consumption();
    expect(defaultConsumption.consumption_date).toBe(today);
    expect(defaultConsumption.meal_type).toBe('snack');
  });

  test('should save consumption data', async () => {
    const consumption = new Consumption({
      id: 'consumption1',
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'breakfast',
      notes: 'Test notes'
    });

    // Mock the database queries
    dbManager.update.mockResolvedValue({ rowsAffected: 1 });

    await consumption.save();

    expect(dbManager.update).toHaveBeenCalledWith('Consumption', 'consumption1', expect.any(Object), 'id');
  });

  test('should create new consumption when id is not provided', async () => {
    const consumption = new Consumption({
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'lunch',
      notes: 'New consumption'
    });

    // Mock the database queries
    dbManager.executeQuery.mockResolvedValue({ rowsAffected: 1 });

    await consumption.save();

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('INSERT INTO Consumption'),
      expect.arrayContaining([consumption.id, 'user1', '2023-01-01', 'lunch'])
    );
    expect(consumption.id).not.toBeNull();
  });

  test('should save consumption items', async () => {
    const consumption = new Consumption({
      id: 'consumption1',
      user_id: 'user1',
      items: [
        { id: 'item1', food_id: 'food1', quantity: 100, unit: 'g' },
        { food_id: 'food2', quantity: 200, unit: 'g' }
      ]
    });

    // Mock the database queries
    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('SELECT ci.*, f.name')) {
        return Promise.resolve({
          rows: {
            length: 1,
            item: () => ({ id: 'item1', food_id: 'food1', quantity: 100, unit: 'g' })
          }
        });
      }
      return Promise.resolve({ rowsAffected: 1 });
    });
    dbManager.executeTransaction.mockResolvedValue();

    await consumption.saveItems();

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT ci.*, f.name'),
      ['consumption1']
    );
    expect(dbManager.executeTransaction).toHaveBeenCalledWith(expect.any(Array));
  });

  test('should delete consumption', async () => {
    const consumption = new Consumption({
      id: 'consumption1',
      user_id: 'user1'
    });

    // Mock the database queries
    dbManager.softDelete.mockResolvedValue({ rowsAffected: 1 });
    dbManager.executeQuery.mockResolvedValue({ rowsAffected: 1 });

    await consumption.delete();

    expect(dbManager.softDelete).toHaveBeenCalledWith('Consumption', 'consumption1', 'id');
    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('UPDATE ConsumptionItem'),
      ['consumption1']
    );
    expect(consumption.is_active).toBe(0);
    expect(consumption.deleted_at).not.toBeNull();
  });

  test('should load consumption items', async () => {
    const consumption = new Consumption({
      id: 'consumption1',
      user_id: 'user1'
    });

    const mockItems = [
      { id: 'item1', food_id: 'food1', quantity: 100, unit: 'g', food_name: 'Test Food' },
      { id: 'item2', food_id: 'food2', quantity: 200, unit: 'g', food_name: 'Another Food' }
    ];

    // Mock the database query
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockItems.length,
        item: (index) => mockItems[index]
      }
    });

    await consumption.loadItems();

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT ci.*, f.name as food_name'),
      ['consumption1']
    );
    expect(consumption.items).toEqual(mockItems);
  });

  test('should calculate nutrition', async () => {
    const consumption = new Consumption({
      id: 'consumption1',
      user_id: 'user1',
      items: [
        { id: 'item1', food_id: 'food1', quantity: 100, unit: 'g' }
      ]
    });

    const nutrition = await consumption.calculateNutrition();

    expect(Food.findById).toHaveBeenCalledWith('food1');
    expect(nutrition.calories).toBe(100);
    expect(nutrition.protein).toBe(10);
    expect(nutrition.carbs).toBe(20);
    expect(nutrition.fat).toBe(5);
    expect(nutrition.nutrientsList).toHaveLength(4);
  });

  test('should find consumption by ID', async () => {
    const mockConsumption = {
      id: 'consumption1',
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'breakfast',
      notes: 'Test notes'
    };

    // Mock the database query
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: 1,
        item: () => mockConsumption
      }
    });

    const consumption = await Consumption.findById('consumption1');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM Consumption WHERE id = ?'),
      ['consumption1']
    );
    expect(consumption).toBeInstanceOf(Consumption);
    expect(consumption.id).toBe('consumption1');
    expect(consumption.meal_type).toBe('breakfast');
  });

  test('should get consumptions by user and date', async () => {
    const mockConsumptions = [
      { id: 'consumption1', user_id: 'user1', consumption_date: '2023-01-01', meal_type: 'breakfast' },
      { id: 'consumption2', user_id: 'user1', consumption_date: '2023-01-01', meal_type: 'lunch' }
    ];

    // Mock the database query
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockConsumptions.length,
        item: (index) => mockConsumptions[index]
      }
    });

    const consumptions = await Consumption.getByUserAndDate('user1', '2023-01-01');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT c.*, mt.name as meal_type_name'),
      ['user1', '2023-01-01']
    );
    expect(consumptions).toHaveLength(2);
    expect(consumptions[0]).toBeInstanceOf(Consumption);
    expect(consumptions[0].id).toBe('consumption1');
    expect(consumptions[1].id).toBe('consumption2');
  });

  test('should get daily nutrition summary', async () => {
    // Mock getByUserAndDate to return consumptions
    const mockConsumptions = [
      new Consumption({
        id: 'consumption1',
        user_id: 'user1',
        consumption_date: '2023-01-01',
        meal_type: 'breakfast',
        items: [{ id: 'item1', food_id: 'food1', quantity: 100, unit: 'g' }]
      }),
      new Consumption({
        id: 'consumption2',
        user_id: 'user1',
        consumption_date: '2023-01-01',
        meal_type: 'lunch',
        items: [{ id: 'item2', food_id: 'food1', quantity: 200, unit: 'g' }]
      })
    ];

    // Mock the static method
    const originalGetByUserAndDate = Consumption.getByUserAndDate;
    Consumption.getByUserAndDate = jest.fn().mockResolvedValue(mockConsumptions);

    // Mock calculateNutrition for each consumption
    mockConsumptions[0].calculateNutrition = jest.fn().mockResolvedValue({
      calories: 100,
      protein: 10,
      carbs: 20,
      fat: 5,
      nutrientsList: [
        { nutrient_id: 'nutrient-calories', name: 'Calories', amount: 100, unit: 'kcal', is_macro: 1 },
        { nutrient_id: 'nutrient-protein', name: 'Protein', amount: 10, unit: 'g', is_macro: 1 }
      ]
    });

    mockConsumptions[1].calculateNutrition = jest.fn().mockResolvedValue({
      calories: 200,
      protein: 20,
      carbs: 40,
      fat: 10,
      nutrientsList: [
        { nutrient_id: 'nutrient-calories', name: 'Calories', amount: 200, unit: 'kcal', is_macro: 1 },
        { nutrient_id: 'nutrient-protein', name: 'Protein', amount: 20, unit: 'g', is_macro: 1 }
      ]
    });

    const summary = await Consumption.getDailyNutritionSummary('user1', '2023-01-01');

    expect(Consumption.getByUserAndDate).toHaveBeenCalledWith('user1', '2023-01-01');
    expect(mockConsumptions[0].calculateNutrition).toHaveBeenCalled();
    expect(mockConsumptions[1].calculateNutrition).toHaveBeenCalled();
    expect(summary.date).toBe('2023-01-01');
    expect(summary.calories).toBe(300);
    expect(summary.protein).toBe(30);
    expect(summary.carbs).toBe(60);
    expect(summary.fat).toBe(15);
    expect(summary.meals).toHaveLength(2);
    expect(summary.nutrientsList).toHaveLength(2);

    // Restore the original method
    Consumption.getByUserAndDate = originalGetByUserAndDate;
  });
});
