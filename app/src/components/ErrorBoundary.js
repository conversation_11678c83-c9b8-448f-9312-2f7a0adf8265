import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Share, Alert } from 'react-native';
import {
  logError,
  log,
  LOG_LEVELS,
  LOG_CATEGORIES,
  exportLogs,
  getDeviceInfo
} from '../services/logService';
import { getCompileIssues, exportCompileIssues } from '../utils/compileTimeLogger';
import { getRuntimeStats, exportRuntimeData } from '../utils/runtimeMonitor';
import { MaterialIcons } from '@expo/vector-icons';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      componentStack: null,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  async componentDidCatch(error, errorInfo) {
    // Enhanced error logging with comprehensive context
    try {
      const deviceInfo = await getDeviceInfo();
      const runtimeStats = getRuntimeStats();
      const compileIssues = getCompileIssues();

      await logError(error, 'ErrorBoundary', {
        componentStack: errorInfo.componentStack,
        critical: true,
        componentName: this.props.componentName || 'Unknown',
        deviceInfo,
        runtimeStats,
        recentCompileIssues: compileIssues.slice(0, 5), // Last 5 compile issues
        timestamp: new Date().toISOString(),
        errorBoundaryProps: {
          showDetails: this.props.showDetails,
          showReportButton: this.props.showReportButton,
        },
      });

      // Log to system category for error boundary specific tracking
      await log(
        LOG_LEVELS.FATAL,
        `Error Boundary Triggered: ${error.message}`,
        {
          componentName: this.props.componentName,
          errorType: error.name,
          hasStack: !!error.stack,
        },
        LOG_CATEGORIES.SYSTEM
      );

    } catch (loggingError) {
      console.error('Error in ErrorBoundary logging:', loggingError);
    }

    this.setState({
      errorInfo,
      componentStack: errorInfo.componentStack,
    });
  }

  resetError = async () => {
    try {
      // Log the error recovery attempt
      await log(
        LOG_LEVELS.INFO,
        'Error Boundary Reset Attempted',
        {
          componentName: this.props.componentName,
          errorMessage: this.state.error?.message,
          resetTimestamp: new Date().toISOString(),
        },
        LOG_CATEGORIES.SYSTEM
      );

      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        componentStack: null,
      });

      // If a reset handler was provided, call it
      if (this.props.onReset) {
        this.props.onReset();
      }
    } catch (error) {
      console.error('Error during error boundary reset:', error);
    }
  }

  generateErrorReport = async () => {
    try {
      const deviceInfo = await getDeviceInfo();
      const allLogs = await exportLogs();
      const compileIssues = exportCompileIssues();
      const runtimeData = exportRuntimeData();

      const errorReport = {
        timestamp: new Date().toISOString(),
        errorBoundary: {
          componentName: this.props.componentName || 'Unknown',
          error: {
            name: this.state.error?.name,
            message: this.state.error?.message,
            stack: this.state.error?.stack,
          },
          componentStack: this.state.componentStack,
        },
        deviceInfo,
        logs: JSON.parse(allLogs),
        compileIssues: JSON.parse(compileIssues),
        runtimeData: JSON.parse(runtimeData),
        appVersion: require('../../package.json').version || 'unknown',
      };

      return JSON.stringify(errorReport, null, 2);
    } catch (error) {
      console.error('Error generating error report:', error);
      return JSON.stringify({
        error: 'Failed to generate complete error report',
        basicError: {
          message: this.state.error?.message,
          timestamp: new Date().toISOString(),
        }
      }, null, 2);
    }
  }

  handleReport = async () => {
    try {
      const errorReport = await this.generateErrorReport();

      if (this.props.onReport) {
        // Use custom report handler if provided
        this.props.onReport(errorReport);
      } else {
        // Default sharing behavior
        await Share.share({
          message: 'Error Report for ZnüniZähler',
          title: 'Error Report',
          url: `data:text/plain;base64,${btoa(errorReport)}`,
        });
      }

      // Log the report generation
      await log(
        LOG_LEVELS.INFO,
        'Error Report Generated',
        {
          componentName: this.props.componentName,
          reportSize: errorReport.length,
          timestamp: new Date().toISOString(),
        },
        LOG_CATEGORIES.SYSTEM
      );

    } catch (error) {
      console.error('Error sharing error report:', error);
      Alert.alert(
        'Report Error',
        'Failed to generate or share error report. Please try again.',
        [{ text: 'OK' }]
      );
    }
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI when an error occurs
      return (
        <View style={styles.container}>
          <View style={styles.header}>
            <MaterialIcons name="error" size={40} color="#fff" />
            <Text style={styles.headerText}>Something went wrong</Text>
          </View>

          <ScrollView style={styles.scrollView}>
            <View style={styles.errorContainer}>
              <Text style={styles.errorTitle}>Error:</Text>
              <Text style={styles.errorMessage}>
                {this.state.error && this.state.error.toString()}
              </Text>

              {this.props.showDetails && this.state.componentStack && (
                <View style={styles.stackContainer}>
                  <Text style={styles.stackTitle}>Component Stack:</Text>
                  <Text style={styles.stackText}>
                    {this.state.componentStack}
                  </Text>
                </View>
              )}
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            {this.props.showReportButton && (
              <TouchableOpacity
                style={[styles.button, styles.reportButton]}
                onPress={this.handleReport}
              >
                <MaterialIcons name="bug-report" size={20} color="#fff" />
                <Text style={styles.buttonText}>Report</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.button, styles.resetButton]}
              onPress={this.resetError}
            >
              <MaterialIcons name="refresh" size={20} color="#fff" />
              <Text style={styles.buttonText}>Try Again</Text>
            </TouchableOpacity>
          </View>

          {this.props.children && this.props.fallback && (
            <View style={styles.fallbackContainer}>
              {this.props.fallback}
            </View>
          )}
        </View>
      );
    }

    // When there's no error, render children normally
    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#d32f2f',
    padding: 20,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  headerText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  scrollView: {
    flex: 1,
  },
  errorContainer: {
    padding: 20,
    backgroundColor: '#fff',
    margin: 10,
    borderRadius: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#d32f2f',
  },
  errorMessage: {
    fontSize: 14,
    color: '#333',
    marginBottom: 15,
  },
  stackContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f8f8f8',
    borderRadius: 5,
  },
  stackTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#666',
  },
  stackText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 15,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginHorizontal: 10,
  },
  resetButton: {
    backgroundColor: '#4caf50',
  },
  reportButton: {
    backgroundColor: '#2196f3',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 5,
  },
  fallbackContainer: {
    padding: 20,
  },
});

export default ErrorBoundary;
