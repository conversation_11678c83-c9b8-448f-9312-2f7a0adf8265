import * as SQLite from 'expo-sqlite';
import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import { Platform } from 'react-native';

/**
 * Database Manager for ZnüniZähler
 * Handles database initialization, migrations, and provides access methods
 */
class DatabaseManager {
  constructor() {
    this.db = null;
    this.initialized = false;
  }

  /**
   * Initialize the database
   * @returns {Promise<void>}
   */
  async init() {
    if (this.initialized) return;

    try {
      // Check if we're running on web
      if (Platform.OS === 'web') {
        console.warn('SQLite not available on web platform. Using mock database.');
        this.db = this._createMockDatabase();
        this.initialized = true;
        return;
      }

      // Open the database
      this.db = SQLite.openDatabase('znunizaehler.db');

      // Check if database needs initialization
      const isInitialized = await this._checkIfInitialized();

      if (!isInitialized) {
        console.log('Initializing database...');
        await this._initializeDatabase();
      } else {
        console.log('Database already initialized');
        // Check for migrations
        await this._checkMigrations();
      }

      this.initialized = true;
    } catch (error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  /**
   * Check if food database is imported
   * @param {string} source - Database source (e.g., 'USDA', 'FoodB')
   * @returns {Promise<boolean>}
   */
  async isFoodDatabaseImported(source) {
    try {
      const result = await this.executeQuery(
        "SELECT COUNT(*) as count FROM Food WHERE source LIKE ?",
        [`${source}%`]
      );

      return result.rows.item(0).count > 0;
    } catch (error) {
      console.error(`Error checking if ${source} database is imported:`, error);
      return false;
    }
  }

  /**
   * Check if the database has been initialized
   * @returns {Promise<boolean>}
   * @private
   */
  _checkIfInitialized() {
    return new Promise((resolve) => {
      this.db.transaction(tx => {
        tx.executeSql(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='User'",
          [],
          (_, result) => {
            resolve(result.rows.length > 0);
          },
          (_, error) => {
            console.error('Error checking database initialization:', error);
            resolve(false);
            return false;
          }
        );
      });
    });
  }

  /**
   * Initialize the database with schema
   * @returns {Promise<void>}
   * @private
   */
  async _initializeDatabase() {
    try {
      // Load SQL schema from asset
      const asset = Asset.fromModule(require('../assets/database/schema.sql'));
      await asset.downloadAsync();

      const sqlSchema = await FileSystem.readAsStringAsync(asset.localUri);
      const statements = sqlSchema.split(';').filter(stmt => stmt.trim().length > 0);

      return new Promise((resolve, reject) => {
        this.db.transaction(
          tx => {
            statements.forEach(statement => {
              tx.executeSql(statement, [],
                () => { },
                (_, error) => {
                  console.error('Error executing SQL statement:', error, statement);
                  return false;
                }
              );
            });
          },
          error => {
            console.error('Transaction error during initialization:', error);
            reject(error);
          },
          () => {
            console.log('Database initialized successfully');
            resolve();
          }
        );
      });
    } catch (error) {
      console.error('Error in database initialization:', error);
      throw error;
    }
  }

  /**
   * Check for and apply database migrations
   * @returns {Promise<void>}
   * @private
   */
  async _checkMigrations() {
    try {
      // Get current database version
      const versionResult = await this.executeQuery(
        'SELECT version FROM DatabaseVersion ORDER BY version DESC LIMIT 1'
      );

      if (versionResult.rows.length === 0) {
        console.warn('No database version found. Creating initial version record.');
        await this.executeQuery(
          'INSERT INTO DatabaseVersion (version, description) VALUES (1, "Initial schema")'
        );
        return;
      }

      const currentVersion = versionResult.rows.item(0).version;
      console.log(`Current database version: ${currentVersion}`);

      // Apply migrations based on current version
      switch (currentVersion) {
        case 1:
        // No migrations yet, but this is where we would add them
        // await this._applyMigrationToVersion2();
        // currentVersion = 2;
        // falls through to next case

        // case 2:
        // await this._applyMigrationToVersion3();
        // currentVersion = 3;
        // falls through to next case

        default:
          // No migrations needed
          break;
      }
    } catch (error) {
      console.error('Error checking migrations:', error);
      throw error;
    }
  }

  /**
   * Apply a migration and update the version
   * @param {number} version - New version number
   * @param {string} description - Migration description
   * @param {Array<{sql: string, params: Array}>} queries - Migration queries
   * @returns {Promise<void>}
   * @private
   */
  async _applyMigration(version, description, queries) {
    try {
      // Add version update query
      queries.push({
        sql: 'INSERT INTO DatabaseVersion (version, description) VALUES (?, ?)',
        params: [version, description]
      });

      // Execute all queries in a transaction
      await this.executeTransaction(queries);

      console.log(`Applied migration to version ${version}: ${description}`);
    } catch (error) {
      console.error(`Error applying migration to version ${version}:`, error);
      throw error;
    }
  }

  /**
   * Create a mock database for web platform
   * @returns {Object} - Mock database object
   * @private
   */
  _createMockDatabase() {
    return {
      transaction: (callback) => {
        const mockTx = {
          executeSql: (sql, params, successCallback, errorCallback) => {
            // Mock successful execution
            if (successCallback) {
              successCallback(null, {
                rows: { length: 0, item: () => null, _array: [] },
                insertId: Math.floor(Math.random() * 1000),
                rowsAffected: 1,
              });
            }
          }
        };
        callback(mockTx);
      }
    };
  }

  /**
   * Execute a SQL query with parameters
   * @param {string} sql - SQL query
   * @param {Array} params - Query parameters
   * @returns {Promise<Object>} - Query result
   */
  executeQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.initialized) {
        reject(new Error('Database not initialized. Call init() first.'));
        return;
      }

      // Handle web platform with mock data
      if (Platform.OS === 'web') {
        resolve({
          rows: {
            length: 0,
            item: () => null,
            _array: []
          },
          insertId: Math.floor(Math.random() * 1000),
          rowsAffected: 1,
        });
        return;
      }

      this.db.transaction(tx => {
        tx.executeSql(
          sql,
          params,
          (_, result) => {
            resolve(result);
          },
          (_, error) => {
            console.error('SQL Error:', error);
            reject(error);
            return false;
          }
        );
      });
    });
  }

  /**
   * Execute multiple SQL queries in a transaction
   * @param {Array<{sql: string, params: Array}>} queries - Array of query objects
   * @returns {Promise<Array>} - Array of results
   */
  executeTransaction(queries) {
    return new Promise((resolve, reject) => {
      if (!this.initialized) {
        reject(new Error('Database not initialized. Call init() first.'));
        return;
      }

      const results = [];

      this.db.transaction(
        tx => {
          queries.forEach(({ sql, params = [] }) => {
            tx.executeSql(
              sql,
              params,
              (_, result) => {
                results.push(result);
              },
              (_, error) => {
                console.error('Transaction SQL Error:', error);
                return false;
              }
            );
          });
        },
        error => {
          console.error('Transaction error:', error);
          reject(error);
        },
        () => {
          resolve(results);
        }
      );
    });
  }

  /**
   * Get all active records from a table
   * @param {string} table - Table name
   * @returns {Promise<Array>} - Array of records
   */
  getAll(table) {
    return this.executeQuery(`SELECT * FROM ${table} WHERE is_active = 1 AND deleted_at IS NULL`)
      .then(result => {
        const items = [];
        for (let i = 0; i < result.rows.length; i++) {
          items.push(result.rows.item(i));
        }
        return items;
      });
  }

  /**
   * Get a record by ID
   * @param {string} table - Table name
   * @param {number} id - Record ID
   * @param {string} idField - ID field name (default: table_id)
   * @returns {Promise<Object>} - Record object
   */
  getById(table, id, idField = null) {
    const idColumn = idField || `${table.replace(/s$/, '')}_id`;
    return this.executeQuery(
      `SELECT * FROM ${table} WHERE ${idColumn} = ? AND is_active = 1 AND deleted_at IS NULL`,
      [id]
    ).then(result => {
      return result.rows.length > 0 ? result.rows.item(0) : null;
    });
  }

  /**
   * Insert a record
   * @param {string} table - Table name
   * @param {Object} data - Record data
   * @returns {Promise<number>} - Inserted record ID
   */
  insert(table, data) {
    const columns = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);

    return this.executeQuery(
      `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`,
      values
    ).then(result => {
      return result.insertId;
    });
  }

  /**
   * Update a record
   * @param {string} table - Table name
   * @param {number} id - Record ID
   * @param {Object} data - Record data
   * @param {string} idField - ID field name (default: table_id)
   * @returns {Promise<number>} - Number of rows affected
   */
  update(table, id, data, idField = null) {
    const idColumn = idField || `${table.replace(/s$/, '')}_id`;
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(data), id];

    return this.executeQuery(
      `UPDATE ${table} SET ${setClause} WHERE ${idColumn} = ?`,
      values
    ).then(result => {
      return result.rowsAffected;
    });
  }

  /**
   * Soft delete a record
   * @param {string} table - Table name
   * @param {number} id - Record ID
   * @param {string} idField - ID field name (default: table_id)
   * @returns {Promise<number>} - Number of rows affected
   */
  softDelete(table, id, idField = null) {
    const idColumn = idField || `${table.replace(/s$/, '')}_id`;
    return this.executeQuery(
      `UPDATE ${table} SET is_active = 0, deleted_at = datetime('now') WHERE ${idColumn} = ?`,
      [id]
    ).then(result => {
      return result.rowsAffected;
    });
  }

  /**
   * Hard delete a record (use with caution)
   * @param {string} table - Table name
   * @param {number} id - Record ID
   * @param {string} idField - ID field name (default: table_id)
   * @returns {Promise<number>} - Number of rows affected
   */
  hardDelete(table, id, idField = null) {
    const idColumn = idField || `${table.replace(/s$/, '')}_id`;
    return this.executeQuery(
      `DELETE FROM ${table} WHERE ${idColumn} = ?`,
      [id]
    ).then(result => {
      return result.rowsAffected;
    });
  }

  /**
   * Query records with custom conditions
   * @param {string} table - Table name
   * @param {Object} conditions - Conditions object
   * @returns {Promise<Array>} - Array of records
   */
  query(table, conditions = {}) {
    let whereClause = 'is_active = 1 AND deleted_at IS NULL';
    const params = [];

    Object.entries(conditions).forEach(([key, value]) => {
      whereClause += ` AND ${key} = ?`;
      params.push(value);
    });

    return this.executeQuery(
      `SELECT * FROM ${table} WHERE ${whereClause}`,
      params
    ).then(result => {
      const items = [];
      for (let i = 0; i < result.rows.length; i++) {
        items.push(result.rows.item(i));
      }
      return items;
    });
  }

  /**
   * Get database version
   * @returns {Promise<number>} - Current database version
   */
  async getDatabaseVersion() {
    try {
      const result = await this.executeQuery(
        'SELECT version FROM DatabaseVersion ORDER BY version DESC LIMIT 1'
      );

      if (result.rows.length === 0) {
        return 0;
      }

      return result.rows.item(0).version;
    } catch (error) {
      console.error('Error getting database version:', error);
      return 0;
    }
  }

  /**
   * Get database statistics
   * @returns {Promise<Object>} - Database statistics
   */
  async getDatabaseStats() {
    try {
      const tables = await this.executeQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );

      const stats = {
        version: await this.getDatabaseVersion(),
        tables: [],
        totalRecords: 0
      };

      for (let i = 0; i < tables.rows.length; i++) {
        const tableName = tables.rows.item(i).name;
        const countResult = await this.executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`);
        const count = countResult.rows.item(0).count;

        stats.tables.push({
          name: tableName,
          records: count
        });

        stats.totalRecords += count;
      }

      return stats;
    } catch (error) {
      console.error('Error getting database stats:', error);
      throw error;
    }
  }

  /**
   * Backup the database
   * @returns {Promise<string>} - Backup file path
   */
  async backupDatabase() {
    try {
      const FileSystem = require('expo-file-system');

      const dbDir = FileSystem.documentDirectory + 'SQLite/';
      const dbPath = dbDir + 'znunizaehler.db';
      const backupPath = FileSystem.documentDirectory + 'backup_' +
        new Date().toISOString().replace(/[:.]/g, '_') + '.db';

      await FileSystem.copyAsync({
        from: dbPath,
        to: backupPath
      });

      return backupPath;
    } catch (error) {
      console.error('Error backing up database:', error);
      throw error;
    }
  }

  /**
   * Reset the database (use with caution)
   * @returns {Promise<void>}
   */
  async resetDatabase() {
    try {
      // Close the current connection
      if (this.db) {
        this.db._db.close();
      }

      // Delete the database file
      const FileSystem = require('expo-file-system');
      const dbDir = FileSystem.documentDirectory + 'SQLite/';
      const dbPath = dbDir + 'znunizaehler.db';

      await FileSystem.deleteAsync(dbPath, { idempotent: true });

      // Reinitialize
      this.db = null;
      this.initialized = false;
      await this.init();

      console.log('Database reset successfully');
    } catch (error) {
      console.error('Error resetting database:', error);
      throw error;
    }
  }

  /**
   * Close the database connection
   */
  close() {
    if (this.db) {
      this.db._db.close();
      this.db = null;
      this.initialized = false;
    }
  }
}

// Export a singleton instance
const dbManager = new DatabaseManager();
export default dbManager;
