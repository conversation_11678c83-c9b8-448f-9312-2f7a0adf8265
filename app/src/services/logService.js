import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Dimensions } from 'react-native';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Alert } from 'react-native';

// Log levels
export const LOG_LEVELS = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR',
  FATAL: 'FATAL',
  COMPILE: 'COMPILE',
  PERFORMANCE: 'PERFORMANCE',
  SECURITY: 'SECURITY',
  NETWORK: 'NETWORK',
  DATABASE: 'DATABASE',
  UI: 'UI',
  IMPORT: 'IMPORT',
};

// Log categories for better organization
export const LOG_CATEGORIES = {
  SYSTEM: 'SYSTEM',
  USER_ACTION: 'USER_ACTION',
  DATA_IMPORT: 'DATA_IMPORT',
  NETWORK: 'NETWORK',
  DATABASE: 'DATABASE',
  UI_INTERACTION: 'UI_INTERACTION',
  PERFORMANCE: 'PERFORMANCE',
  SECURITY: 'SECURITY',
  COMPILATION: 'COMPILATION',
  RUNTIME: 'RUNTIME',
};

// Storage keys
const LOGS_STORAGE_KEY = '@nutrition_tracker:logs';
const ERROR_LOGS_STORAGE_KEY = '@nutrition_tracker:error_logs';
const PERFORMANCE_LOGS_STORAGE_KEY = '@nutrition_tracker:performance_logs';
const COMPILE_LOGS_STORAGE_KEY = '@nutrition_tracker:compile_logs';

// Configuration
const MAX_LOGS = 500;
const MAX_ERROR_LOGS = 200;
const MAX_PERFORMANCE_LOGS = 100;
const MAX_COMPILE_LOGS = 50;
const LOG_RETENTION_DAYS = 7;
const ENABLE_CONSOLE_LOGGING = __DEV__;
const ENABLE_FILE_LOGGING = true;
const ENABLE_REMOTE_LOGGING = false; // Set to true for production with remote endpoint

/**
 * Get device information for logging context
 */
export const getDeviceInfo = async () => {
  try {
    return {
      deviceName: Device.deviceName || 'Unknown',
      deviceYearClass: await Device.getDeviceYearClassAsync() || 'Unknown',
      osName: Device.osName || 'Unknown',
      osVersion: Device.osVersion || 'Unknown',
      manufacturer: Device.manufacturer || 'Unknown',
      modelName: Device.modelName || 'Unknown',
      screenSize: `${Dimensions.get('window').width}x${Dimensions.get('window').height}`,
      platform: Platform.OS,
      platformVersion: Platform.Version,
      expoVersion: Constants.expoVersion || 'Unknown',
      appVersion: Constants.manifest?.version || 'Unknown',
      isDevice: Device.isDevice,
    };
  } catch (error) {
    console.error('Error getting device info:', error);
    return {
      error: 'Failed to get device info',
      platform: Platform.OS,
    };
  }
};

/**
 * Enhanced log function with categorization and filtering
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {Object} metadata - Additional metadata
 * @param {string} category - Log category
 */
export const log = async (level, message, metadata = {}, category = LOG_CATEGORIES.SYSTEM) => {
  try {
    // Create enhanced log entry
    const logEntry = {
      id: generateLogId(),
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      metadata: {
        ...metadata,
        sessionId: await getSessionId(),
        userId: metadata.userId || 'anonymous',
        buildVersion: Constants.expoConfig?.version || 'unknown',
        platform: Platform.OS,
        platformVersion: Platform.Version,
      },
      stackTrace: level === LOG_LEVELS.ERROR || level === LOG_LEVELS.FATAL ? new Error().stack : null,
    };

    // Console logging (only in development)
    if (ENABLE_CONSOLE_LOGGING) {
      logToConsole(level, message, metadata);
    }

    // Store in appropriate storage based on level
    await storeLogEntry(logEntry);

    // File logging for critical errors
    if (ENABLE_FILE_LOGGING && (level === LOG_LEVELS.ERROR || level === LOG_LEVELS.FATAL)) {
      await logToFile(logEntry);
    }

    // Remote logging for production
    if (ENABLE_REMOTE_LOGGING && !__DEV__) {
      await logToRemote(logEntry);
    }

    // Trigger alerts for critical errors
    if (level === LOG_LEVELS.FATAL) {
      await handleCriticalError(logEntry);
    }

    return true;
  } catch (error) {
    console.error('Error in logging system:', error);
    return false;
  }
};

/**
 * Log to console with appropriate method
 */
const logToConsole = (level, message, metadata) => {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level}]`;

  switch (level) {
    case LOG_LEVELS.DEBUG:
      console.debug(prefix, message, metadata);
      break;
    case LOG_LEVELS.INFO:
      console.info(prefix, message, metadata);
      break;
    case LOG_LEVELS.WARN:
      console.warn(prefix, message, metadata);
      break;
    case LOG_LEVELS.ERROR:
    case LOG_LEVELS.FATAL:
      console.error(prefix, message, metadata);
      break;
    case LOG_LEVELS.PERFORMANCE:
      console.time ? console.time(message) : console.log(prefix, message, metadata);
      break;
    default:
      console.log(prefix, message, metadata);
  }
};

/**
 * Store log entry in appropriate storage
 */
const storeLogEntry = async (logEntry) => {
  try {
    let storageKey = LOGS_STORAGE_KEY;
    let maxLogs = MAX_LOGS;

    // Use specialized storage for different log types
    switch (logEntry.level) {
      case LOG_LEVELS.ERROR:
      case LOG_LEVELS.FATAL:
        storageKey = ERROR_LOGS_STORAGE_KEY;
        maxLogs = MAX_ERROR_LOGS;
        break;
      case LOG_LEVELS.PERFORMANCE:
        storageKey = PERFORMANCE_LOGS_STORAGE_KEY;
        maxLogs = MAX_PERFORMANCE_LOGS;
        break;
      case LOG_LEVELS.COMPILE:
        storageKey = COMPILE_LOGS_STORAGE_KEY;
        maxLogs = MAX_COMPILE_LOGS;
        break;
    }

    const storedLogsJson = await AsyncStorage.getItem(storageKey);
    const storedLogs = storedLogsJson ? JSON.parse(storedLogsJson) : [];

    // Add new log and limit size
    const updatedLogs = [logEntry, ...storedLogs].slice(0, maxLogs);

    await AsyncStorage.setItem(storageKey, JSON.stringify(updatedLogs));
  } catch (error) {
    console.error('Error storing log entry:', error);
  }
};

/**
 * Generate unique log ID
 */
const generateLogId = () => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Get or create session ID
 */
let sessionId = null;
const getSessionId = async () => {
  if (!sessionId) {
    try {
      sessionId = await AsyncStorage.getItem('@nutrition_tracker:session_id');
      if (!sessionId) {
        sessionId = generateLogId();
        await AsyncStorage.setItem('@nutrition_tracker:session_id', sessionId);
      }
    } catch (error) {
      sessionId = generateLogId();
    }
  }
  return sessionId;
};

/**
 * Log to file for critical errors
 */
const logToFile = async (logEntry) => {
  try {
    if (!FileSystem.documentDirectory) return;

    const logDir = `${FileSystem.documentDirectory}logs/`;
    const dirInfo = await FileSystem.getInfoAsync(logDir);

    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(logDir, { intermediates: true });
    }

    const fileName = `error_log_${new Date().toISOString().split('T')[0]}.json`;
    const filePath = `${logDir}${fileName}`;

    let existingLogs = [];
    try {
      const fileContent = await FileSystem.readAsStringAsync(filePath);
      existingLogs = JSON.parse(fileContent);
    } catch (error) {
      // File doesn't exist or is corrupted, start fresh
    }

    existingLogs.push(logEntry);
    await FileSystem.writeAsStringAsync(filePath, JSON.stringify(existingLogs, null, 2));
  } catch (error) {
    console.error('Error writing log to file:', error);
  }
};

/**
 * Log to remote endpoint (for production)
 */
const logToRemote = async (logEntry) => {
  try {
    // Only send critical logs to remote
    if (logEntry.level !== LOG_LEVELS.ERROR && logEntry.level !== LOG_LEVELS.FATAL) {
      return;
    }

    // Replace with your actual logging endpoint
    const endpoint = 'https://your-logging-service.com/api/logs';

    await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY',
      },
      body: JSON.stringify(logEntry),
    });
  } catch (error) {
    // Silently fail remote logging to avoid infinite loops
    console.warn('Remote logging failed:', error.message);
  }
};

/**
 * Handle critical errors
 */
const handleCriticalError = async (logEntry) => {
  try {
    // Store critical error separately
    const criticalErrors = await AsyncStorage.getItem('@nutrition_tracker:critical_errors') || '[]';
    const errors = JSON.parse(criticalErrors);
    errors.unshift(logEntry);

    // Keep only last 10 critical errors
    const limitedErrors = errors.slice(0, 10);
    await AsyncStorage.setItem('@nutrition_tracker:critical_errors', JSON.stringify(limitedErrors));

    // In development, show alert
    if (__DEV__) {
      Alert.alert(
        'Critical Error Detected',
        `${logEntry.message}\n\nCheck debug logs for details.`,
        [{ text: 'OK' }]
      );
    }
  } catch (error) {
    console.error('Error handling critical error:', error);
  }
};

/**
 * Get all stored logs with filtering options
 * @param {string} level - Filter by log level
 * @param {string} category - Filter by category
 * @param {number} limit - Limit number of results
 * @returns {Promise<Array>} - Array of log entries
 */
export const getLogs = async (level = null, category = null, limit = null) => {
  try {
    const allStorageKeys = [
      LOGS_STORAGE_KEY,
      ERROR_LOGS_STORAGE_KEY,
      PERFORMANCE_LOGS_STORAGE_KEY,
      COMPILE_LOGS_STORAGE_KEY,
    ];

    let allLogs = [];

    for (const key of allStorageKeys) {
      try {
        const logsJson = await AsyncStorage.getItem(key);
        if (logsJson) {
          const logs = JSON.parse(logsJson);
          allLogs = allLogs.concat(logs);
        }
      } catch (error) {
        console.warn(`Error reading logs from ${key}:`, error);
      }
    }

    // Sort by timestamp (newest first)
    allLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply filters
    let filteredLogs = allLogs;

    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(0, limit);
    }

    return filteredLogs;
  } catch (error) {
    console.error('Error getting logs:', error);
    return [];
  }
};

/**
 * Clear all stored logs
 * @returns {Promise<boolean>} - Success status
 */
export const clearLogs = async () => {
  try {
    await AsyncStorage.removeItem(LOGS_STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing logs:', error);
    return false;
  }
};

/**
 * Export logs as a string
 * @returns {Promise<string>} - Logs as a string
 */
export const exportLogs = async () => {
  try {
    const logs = await getLogs();
    const deviceInfo = await getDeviceInfo();

    const exportData = {
      deviceInfo,
      logs,
      exportedAt: new Date().toISOString(),
    };

    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    console.error('Error exporting logs:', error);
    return JSON.stringify({ error: 'Failed to export logs' });
  }
};

/**
 * Specialized logging functions
 */

/**
 * Log compilation errors and warnings
 * @param {string} type - 'error' or 'warning'
 * @param {string} message - Compilation message
 * @param {Object} details - File, line, column info
 */
export const logCompileIssue = async (type, message, details = {}) => {
  await log(
    LOG_LEVELS.COMPILE,
    `Compilation ${type}: ${message}`,
    {
      type,
      file: details.file,
      line: details.line,
      column: details.column,
      severity: type === 'error' ? 'high' : 'medium',
      ...details,
    },
    LOG_CATEGORIES.COMPILATION
  );
};

/**
 * Log performance metrics
 * @param {string} operation - Operation name
 * @param {number} duration - Duration in milliseconds
 * @param {Object} metadata - Additional performance data
 */
export const logPerformance = async (operation, duration, metadata = {}) => {
  await log(
    LOG_LEVELS.PERFORMANCE,
    `Performance: ${operation} took ${duration}ms`,
    {
      operation,
      duration,
      timestamp: Date.now(),
      memoryUsage: getMemoryUsage(),
      ...metadata,
    },
    LOG_CATEGORIES.PERFORMANCE
  );
};

/**
 * Log network requests and responses
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {number} status - Response status
 * @param {number} duration - Request duration
 * @param {Object} metadata - Additional network data
 */
export const logNetwork = async (method, url, status, duration, metadata = {}) => {
  const level = status >= 400 ? LOG_LEVELS.ERROR : LOG_LEVELS.INFO;

  await log(
    level,
    `Network: ${method} ${url} - ${status} (${duration}ms)`,
    {
      method,
      url,
      status,
      duration,
      success: status < 400,
      ...metadata,
    },
    LOG_CATEGORIES.NETWORK
  );
};

/**
 * Log database operations
 * @param {string} operation - Database operation
 * @param {string} table - Table name
 * @param {number} duration - Operation duration
 * @param {Object} metadata - Additional database data
 */
export const logDatabase = async (operation, table, duration, metadata = {}) => {
  await log(
    LOG_LEVELS.DATABASE,
    `Database: ${operation} on ${table} (${duration}ms)`,
    {
      operation,
      table,
      duration,
      ...metadata,
    },
    LOG_CATEGORIES.DATABASE
  );
};

/**
 * Log user interactions
 * @param {string} action - User action
 * @param {string} component - UI component
 * @param {Object} metadata - Additional interaction data
 */
export const logUserAction = async (action, component, metadata = {}) => {
  await log(
    LOG_LEVELS.INFO,
    `User Action: ${action} on ${component}`,
    {
      action,
      component,
      timestamp: Date.now(),
      ...metadata,
    },
    LOG_CATEGORIES.USER_ACTION
  );
};

/**
 * Log security events
 * @param {string} event - Security event type
 * @param {string} severity - Event severity
 * @param {Object} metadata - Additional security data
 */
export const logSecurity = async (event, severity, metadata = {}) => {
  await log(
    LOG_LEVELS.SECURITY,
    `Security: ${event} (${severity})`,
    {
      event,
      severity,
      timestamp: Date.now(),
      ...metadata,
    },
    LOG_CATEGORIES.SECURITY
  );
};

/**
 * Enhanced error logging with stack trace and context
 * @param {Error} error - Error object
 * @param {string} context - Context where the error occurred
 * @param {Object} additionalData - Additional data about the error
 */
export const logError = async (error, context, additionalData = {}) => {
  try {
    const errorData = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      context,
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location?.href : 'N/A',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A',
      ...additionalData,
    };

    const level = additionalData.critical ? LOG_LEVELS.FATAL : LOG_LEVELS.ERROR;

    await log(
      level,
      `Error in ${context}: ${error.message}`,
      errorData,
      LOG_CATEGORIES.RUNTIME
    );

    // For critical errors, also capture device info
    if (additionalData.critical) {
      const deviceInfo = await getDeviceInfo();
      await log(LOG_LEVELS.ERROR, 'Device info for critical error', { deviceInfo });
    }

    return true;
  } catch (logError) {
    console.error('Error logging error:', logError);
    return false;
  }
};

/**
 * Get memory usage information
 */
const getMemoryUsage = () => {
  try {
    if (typeof performance !== 'undefined' && performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
      };
    }
    return null;
  } catch (error) {
    return null;
  }
};

/**
 * Performance monitoring wrapper
 * @param {string} operationName - Name of the operation
 * @param {Function} operation - Function to monitor
 * @param {Object} metadata - Additional metadata
 * @returns {Promise<any>} - Result of the operation
 */
export const withPerformanceLogging = async (operationName, operation, metadata = {}) => {
  const startTime = Date.now();
  const startMemory = getMemoryUsage();

  try {
    const result = await operation();
    const duration = Date.now() - startTime;
    const endMemory = getMemoryUsage();

    await logPerformance(operationName, duration, {
      ...metadata,
      success: true,
      startMemory,
      endMemory,
      memoryDelta: endMemory && startMemory ?
        endMemory.used - startMemory.used : null,
    });

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;

    await logPerformance(operationName, duration, {
      ...metadata,
      success: false,
      error: error.message,
    });

    await logError(error, `Performance monitoring: ${operationName}`, {
      duration,
      ...metadata,
    });

    throw error;
  }
};

/**
 * Network request wrapper with logging
 * @param {string} url - Request URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} - Fetch response
 */
export const loggedFetch = async (url, options = {}) => {
  const startTime = Date.now();
  const method = options.method || 'GET';

  try {
    const response = await fetch(url, options);
    const duration = Date.now() - startTime;

    await logNetwork(method, url, response.status, duration, {
      requestSize: options.body ? JSON.stringify(options.body).length : 0,
      responseSize: response.headers.get('content-length') || 'unknown',
    });

    return response;
  } catch (error) {
    const duration = Date.now() - startTime;

    await logNetwork(method, url, 0, duration, {
      error: error.message,
      requestSize: options.body ? JSON.stringify(options.body).length : 0,
    });

    throw error;
  }
};

/**
 * Database operation wrapper with logging
 * @param {string} operation - Operation name
 * @param {string} table - Table name
 * @param {Function} dbOperation - Database operation function
 * @param {Object} metadata - Additional metadata
 * @returns {Promise<any>} - Operation result
 */
export const withDatabaseLogging = async (operation, table, dbOperation, metadata = {}) => {
  const startTime = Date.now();

  try {
    const result = await dbOperation();
    const duration = Date.now() - startTime;

    await logDatabase(operation, table, duration, {
      ...metadata,
      success: true,
      resultCount: Array.isArray(result) ? result.length : 1,
    });

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;

    await logDatabase(operation, table, duration, {
      ...metadata,
      success: false,
      error: error.message,
    });

    await logError(error, `Database operation: ${operation} on ${table}`, {
      duration,
      ...metadata,
    });

    throw error;
  }
};
