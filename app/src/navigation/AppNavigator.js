import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Platform, Text } from 'react-native';
import { logError, log, LOG_LEVELS } from '../services/logService';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import ScanScreen from '../screens/ScanScreen';
import BarcodeScreen from '../screens/BarcodeScreen';
import ManualEntryScreen from '../screens/ManualEntryScreen';
import FoodDetailsScreen from '../screens/FoodDetailsScreen';
import StatisticsScreen from '../screens/StatisticsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import DebugScreen from '../screens/DebugScreen';
import FoodDatabaseScreen from '../screens/FoodDatabaseScreen';
import ImportDatabaseScreen from '../screens/ImportDatabaseScreen';
import CNFImportScreen from '../screens/CNFImportScreen';
import PhenolExplorerImportScreen from '../screens/PhenolExplorerImportScreen';
import FoodImportScreen from '../screens/FoodImportScreen';

// Create stack navigator
const Stack = createStackNavigator();

const AppNavigator = () => {
  // Log app initialization
  React.useEffect(() => {
    const logAppStart = async () => {
      try {
        await log(LOG_LEVELS.INFO, 'Application initialized', {
          platform: Platform.OS,
          platformVersion: Platform.Version,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to log app initialization:', error);
      }
    };

    logAppStart();
  }, []);

  // Handle navigation state changes for logging
  const onNavigationStateChange = (state) => {
    try {
      if (state) {
        const currentRouteName = state.routes[state.index]?.name;
        if (currentRouteName) {
          log(LOG_LEVELS.INFO, `Navigated to: ${currentRouteName}`);
        }
      }
    } catch (error) {
      console.error('Navigation state change error:', error);
    }
  };

  // Handle navigation errors
  const onNavigationError = (error) => {
    logError(error, 'NavigationContainer', {
      critical: true,
      message: 'Navigation error occurred',
    });
  };

  return (
    <NavigationContainer
      onStateChange={onNavigationStateChange}
      onError={onNavigationError}
      fallback={<Text>Loading...</Text>}
    >
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#4CAF50',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{ title: 'ZnüniZähler' }}
        />
        <Stack.Screen
          name="Scan"
          component={ScanScreen}
          options={{ title: 'Scan Food Label' }}
        />
        <Stack.Screen
          name="Barcode"
          component={BarcodeScreen}
          options={{ title: 'Scan Barcode' }}
        />
        <Stack.Screen
          name="ManualEntry"
          component={ManualEntryScreen}
          options={{ title: 'Manual Entry' }}
        />
        <Stack.Screen
          name="FoodDetails"
          component={FoodDetailsScreen}
          options={{ title: 'Food Details' }}
        />
        <Stack.Screen
          name="Statistics"
          component={StatisticsScreen}
          options={{ title: 'Statistics' }}
        />
        <Stack.Screen
          name="Profile"
          component={ProfileScreen}
          options={{ title: 'Profile' }}
        />
        <Stack.Screen
          name="Debug"
          component={DebugScreen}
          options={{
            title: 'Debug Information',
            headerStyle: {
              backgroundColor: '#2196F3',
            },
          }}
        />
        <Stack.Screen
          name="FoodDatabase"
          component={FoodDatabaseScreen}
          options={{
            title: 'Food Database',
            headerStyle: {
              backgroundColor: '#8BC34A',
            },
          }}
        />
        <Stack.Screen
          name="ImportDatabase"
          component={ImportDatabaseScreen}
          options={{
            title: 'Import Database',
            headerStyle: {
              backgroundColor: '#8BC34A',
            },
          }}
        />
        <Stack.Screen
          name="FoodImport"
          component={FoodImportScreen}
          options={{
            title: 'Import Food Data',
            headerStyle: {
              backgroundColor: '#8BC34A',
            },
          }}
        />
        <Stack.Screen
          name="CNFImport"
          component={CNFImportScreen}
          options={{
            title: 'Canadian Nutrient File',
            headerStyle: {
              backgroundColor: '#FF9800',
            },
          }}
        />
        <Stack.Screen
          name="PhenolExplorerImport"
          component={PhenolExplorerImportScreen}
          options={{
            title: 'Phenol-Explorer',
            headerStyle: {
              backgroundColor: '#9C27B0',
            },
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
