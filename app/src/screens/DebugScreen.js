import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Share,
  Alert,
  RefreshControl
} from 'react-native';
import { Card, Button, List, Divider, SegmentedButtons } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { getLogs, clearLogs, exportLogs, getDeviceInfo, LOG_LEVELS } from '../services/logService';
import LoggingDashboard from '../components/LoggingDashboard';
import { getCompileIssueStats } from '../utils/compileTimeLogger';
import { getRuntimeStats } from '../utils/runtimeMonitor';

const DebugScreen = ({ navigation }) => {
  const [logs, setLogs] = useState([]);
  const [deviceInfo, setDeviceInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [compileStats, setCompileStats] = useState({});
  const [runtimeStats, setRuntimeStats] = useState({});

  const loadData = async () => {
    try {
      setLoading(true);
      const logsData = await getLogs();
      const deviceInfoData = await getDeviceInfo();

      setLogs(logsData);
      setDeviceInfo(deviceInfoData);
    } catch (error) {
      console.error('Error loading debug data:', error);
      Alert.alert('Error', 'Failed to load debug data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
  };

  const handleClearLogs = async () => {
    Alert.alert(
      'Clear Logs',
      'Are you sure you want to clear all logs?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearLogs();
              setLogs([]);
              Alert.alert('Success', 'Logs cleared successfully');
            } catch (error) {
              console.error('Error clearing logs:', error);
              Alert.alert('Error', 'Failed to clear logs');
            }
          },
        },
      ]
    );
  };

  const handleExportLogs = async () => {
    try {
      const exportData = await exportLogs();

      await Share.share({
        message: exportData,
        title: 'Nutrition Tracker Logs',
      });
    } catch (error) {
      console.error('Error exporting logs:', error);
      Alert.alert('Error', 'Failed to export logs');
    }
  };

  const getLogLevelColor = (level) => {
    switch (level) {
      case LOG_LEVELS.DEBUG:
        return '#2196f3';
      case LOG_LEVELS.INFO:
        return '#4caf50';
      case LOG_LEVELS.WARN:
        return '#ff9800';
      case LOG_LEVELS.ERROR:
      case LOG_LEVELS.FATAL:
        return '#f44336';
      default:
        return '#757575';
    }
  };

  const formatTimestamp = (timestamp) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (error) {
      return timestamp;
    }
  };

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'logs':
        return <LoggingDashboard />;
      case 'device':
        return renderDeviceTab();
      default:
        return renderOverviewTab();
    }
  };

  // Render overview tab
  const renderOverviewTab = () => (
    <ScrollView
      style={styles.scrollView}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <Text style={styles.loadingText}>Loading debug information...</Text>
        </View>
      ) : (
        <>
          {/* System Status Cards */}
          <View style={styles.statusContainer}>
            <Card style={styles.statusCard}>
              <Card.Content>
                <Text style={styles.statusTitle}>System Logs</Text>
                <Text style={styles.statusValue}>{logs.length}</Text>
                <Text style={styles.statusSubtitle}>
                  {logs.filter(log => log.level === LOG_LEVELS.ERROR).length} errors
                </Text>
              </Card.Content>
            </Card>

            <Card style={styles.statusCard}>
              <Card.Content>
                <Text style={styles.statusTitle}>Compile Issues</Text>
                <Text style={styles.statusValue}>{compileStats.total || 0}</Text>
                <Text style={styles.statusSubtitle}>
                  {compileStats.errors || 0} errors
                </Text>
              </Card.Content>
            </Card>

            <Card style={styles.statusCard}>
              <Card.Content>
                <Text style={styles.statusTitle}>Runtime Status</Text>
                <Text style={styles.statusValue}>
                  {runtimeStats.isMonitoring ? 'Active' : 'Inactive'}
                </Text>
                <Text style={styles.statusSubtitle}>
                  {runtimeStats.errorCount || 0} runtime errors
                </Text>
              </Card.Content>
            </Card>
          </View>

          {/* Quick Actions */}
          <Card style={styles.actionCard}>
            <Card.Content>
              <Text style={styles.cardTitle}>Quick Actions</Text>
              <View style={styles.actionButtonContainer}>
                <Button
                  mode="contained"
                  onPress={handleExportLogs}
                  icon="download"
                  style={styles.actionButton}
                >
                  Export All Logs
                </Button>
                <Button
                  mode="outlined"
                  onPress={handleClearLogs}
                  icon="delete"
                  style={styles.actionButton}
                >
                  Clear Logs
                </Button>
              </View>
            </Card.Content>
          </Card>

          {/* Recent Critical Issues */}
          <Card style={styles.issuesCard}>
            <Card.Content>
              <Text style={styles.cardTitle}>Recent Critical Issues</Text>
              <Divider style={styles.divider} />

              {logs
                .filter(log => log.level === LOG_LEVELS.ERROR || log.level === LOG_LEVELS.FATAL)
                .slice(0, 5)
                .map((log, index) => (
                  <List.Item
                    key={index}
                    title={log.message}
                    description={formatTimestamp(log.timestamp)}
                    left={props => (
                      <List.Icon
                        {...props}
                        icon="alert-circle"
                        color={getLogLevelColor(log.level)}
                      />
                    )}
                  />
                ))}

              {logs.filter(log => log.level === LOG_LEVELS.ERROR || log.level === LOG_LEVELS.FATAL).length === 0 && (
                <Text style={styles.noIssuesText}>No critical issues found</Text>
              )}
            </Card.Content>
          </Card>
        </>
      )}
    </ScrollView>
  );

  // Render device tab
  const renderDeviceTab = () => (
    <ScrollView style={styles.scrollView}>
      <Card style={styles.deviceCard}>
        <Card.Content>
          <Text style={styles.cardTitle}>Device Information</Text>
          <Divider style={styles.divider} />

          {deviceInfo ? (
            Object.entries(deviceInfo).map(([key, value]) => (
              <List.Item
                key={key}
                title={key}
                description={typeof value === 'boolean' ? value.toString() : value}
                left={props => <List.Icon {...props} icon="information" />}
              />
            ))
          ) : (
            <Text style={styles.noDataText}>No device information available</Text>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Debug Information</Text>
      </View>

      {/* Tab Navigation */}
      <SegmentedButtons
        value={activeTab}
        onValueChange={setActiveTab}
        buttons={[
          { value: 'overview', label: 'Overview' },
          { value: 'logs', label: 'Logs' },
          { value: 'device', label: 'Device' },
        ]}
        style={styles.tabButtons}
      />

      {/* Tab Content */}
      {renderTabContent()}

    </View >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  tabButtons: {
    margin: 16,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 16,
    marginBottom: 8,
  },
  statusCard: {
    flex: 1,
    marginHorizontal: 4,
  },
  statusTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statusValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  statusSubtitle: {
    fontSize: 10,
    color: '#999',
  },
  actionCard: {
    margin: 16,
    marginTop: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  actionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  issuesCard: {
    margin: 16,
    marginTop: 8,
  },
  divider: {
    marginBottom: 16,
  },
  noIssuesText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
  },
  deviceCard: {
    margin: 16,
  },
  noDataText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
  },

});

export default DebugScreen;
