/**
 * Food Import Screen for ZnüniZähler
 * Allows users to import food data from various sources
 */

import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, Alert, Linking } from 'react-native';
import { Card, Text, Button, ProgressBar, Chip, List, Divider, ActivityIndicator, SegmentedButtons } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import USDAImporter from '../utils/importers/USDAImporter';
import FoodBImporter from '../utils/importers/FoodBImporter';
import CNFImporter from '../utils/importers/CNFImporter';
import PhenolExplorerImporter from '../utils/importers/PhenolExplorerImporter';

/**
 * Food Import Screen Component
 * @param {Object} props - Component props
 * @param {Object} props.navigation - Navigation object
 * @returns {JSX.Element} - Food import screen component
 */
const FoodImportScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importStats, setImportStats] = useState(null);
  const [datasetType, setDatasetType] = useState('');
  const [error, setError] = useState('');
  const [importSource, setImportSource] = useState('usda');

  // Create importer instances
  const usdaImporter = new USDAImporter();
  const foodbImporter = new FoodBImporter();
  const cnfImporter = new CNFImporter();
  const phenolImporter = new PhenolExplorerImporter();

  // Handle import button press
  const handleImport = async () => {
    try {
      setIsImporting(true);
      setImportProgress(0.1);
      setError('');

      // Show information alert based on selected source
      const alertMessages = {
        usda: {
          title: 'USDA Food Data Import',
          message: 'You will be prompted to select a USDA Food Data Central dataset ZIP file. ' +
            'You can download these datasets from https://fdc.nal.usda.gov/download-datasets.html\n\n' +
            'The import process may take several minutes depending on the dataset size.'
        },
        cnf: {
          title: 'Canadian Nutrient File Import',
          message: 'You will be prompted to select a CNF dataset ZIP file. ' +
            'You can download the dataset from the Health Canada website.\n\n' +
            'This will add Canadian food composition data to your database.'
        },
        phenol: {
          title: 'Phenol-Explorer Import',
          message: 'You will be prompted to select polyphenol data (CSV or ZIP). ' +
            'You can download data from http://phenol-explorer.eu/downloads\n\n' +
            'This will add antioxidant and polyphenol tracking capabilities.'
        },
        foodb: {
          title: 'FoodB Database Import',
          message: 'You will be prompted to select a FoodB dataset ZIP file. ' +
            'You can download the dataset from https://foodb.ca/public/system/downloads/foodb_2020_04_07_json.zip\n\n' +
            'The import process may take several minutes depending on the dataset size.'
        }
      };

      const alert = alertMessages[importSource];
      Alert.alert(alert.title, alert.message, [{ text: 'OK', onPress: startImport }]);
    } catch (error) {
      setError(error.message);
      setIsImporting(false);
    }
  };

  // Start the import process
  const startImport = async () => {
    try {
      // Start import based on selected source
      setImportProgress(0.2);

      let result;
      switch (importSource) {
        case 'usda':
          result = await usdaImporter.startImport();
          break;
        case 'cnf':
          result = await cnfImporter.startImport();
          break;
        case 'phenol':
          result = await phenolImporter.startImport();
          break;
        case 'foodb':
          result = await foodbImporter.startImport();
          break;
        default:
          throw new Error('Unknown import source');
      }

      setImportProgress(1);
      setImportStats(result.stats);
      setDatasetType(result.datasetType || importSource);

      if (!result.success) {
        setError(result.error || 'Import failed');
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setIsImporting(false);
    }
  };

  // Format dataset type for display
  const formatDatasetType = (type) => {
    switch (importSource) {
      case 'foodb':
        return 'FoodB Database';
      case 'cnf':
        return 'Canadian Nutrient File';
      case 'phenol':
        return 'Phenol-Explorer Database';
      case 'usda':
        switch (type) {
          case 'foundation':
            return 'USDA Foundation Foods';
          case 'sr_legacy':
            return 'USDA SR Legacy';
          case 'branded':
            return 'USDA Branded Foods';
          case 'survey':
            return 'USDA Survey Foods (FNDDS)';
          default:
            return 'USDA Database';
        }
      default:
        return 'Unknown Database';
    }
  };

  // Get website URL based on import source
  const getWebsiteUrl = () => {
    switch (importSource) {
      case 'usda':
        return 'https://fdc.nal.usda.gov/download-datasets.html';
      case 'cnf':
        return 'https://www.canada.ca/en/health-canada/services/food-nutrition/healthy-eating/nutrient-data/canadian-nutrient-file-2015-download-files.html';
      case 'phenol':
        return 'http://phenol-explorer.eu/downloads';
      case 'foodb':
        return 'https://foodb.ca/downloads';
      default:
        return 'https://example.com';
    }
  };

  // Get button label based on import source
  const getButtonLabel = () => {
    switch (importSource) {
      case 'usda':
        return 'Select USDA Dataset';
      case 'cnf':
        return 'Select CNF Dataset';
      case 'phenol':
        return 'Select Phenol Data';
      case 'foodb':
        return 'Select FoodB Dataset';
      default:
        return 'Select Dataset';
    }
  };

  // Get website button label based on import source
  const getWebsiteLabel = () => {
    switch (importSource) {
      case 'usda':
        return 'Visit USDA Website';
      case 'cnf':
        return 'Visit Health Canada';
      case 'phenol':
        return 'Visit Phenol-Explorer';
      case 'foodb':
        return 'Visit FoodB Website';
      default:
        return 'Visit Website';
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Card style={styles.card}>
        <Card.Title title="Food Database Import" />
        <Card.Content>
          <Text style={styles.description}>
            Import food data from various nutrition databases to enhance your food tracking experience.
          </Text>

          <SegmentedButtons
            value={importSource}
            onValueChange={setImportSource}
            buttons={[
              { value: 'usda', label: 'USDA' },
              { value: 'cnf', label: 'CNF' },
              { value: 'phenol', label: 'Phenol' },
              { value: 'foodb', label: 'FoodB' }
            ]}
            style={styles.segmentedButtons}
          />

          <Button
            mode="contained"
            icon="database-import"
            onPress={handleImport}
            disabled={isImporting}
            style={styles.importButton}
          >
            {getButtonLabel()}
          </Button>

          <Button
            mode="outlined"
            icon="web"
            onPress={() => Linking.openURL(getWebsiteUrl())}
            style={styles.websiteButton}
          >
            {getWebsiteLabel()}
          </Button>

          {isImporting && (
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>Importing data...</Text>
              <ProgressBar progress={importProgress} color={theme.colors.primary} style={styles.progressBar} />
              <ActivityIndicator animating={true} color={theme.colors.primary} style={styles.spinner} />
            </View>
          )}

          {error ? (
            <View style={styles.errorContainer}>
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                Error: {error}
              </Text>
            </View>
          ) : null}

          {importStats && (
            <View style={styles.statsContainer}>
              <Text style={styles.statsTitle}>Import Results</Text>

              <Chip icon="information" style={styles.datasetChip}>
                Dataset: {formatDatasetType(datasetType)}
              </Chip>

              <List.Section>
                <List.Item
                  title="Foods Imported"
                  description={importStats.foods.toString()}
                  left={props => <List.Icon {...props} icon="food-apple" />}
                />
                <Divider />
                <List.Item
                  title="Nutrients Added"
                  description={importStats.nutrients.toString()}
                  left={props => <List.Icon {...props} icon="molecule" />}
                />
                <Divider />
                <List.Item
                  title="Ingredients Added"
                  description={importStats.ingredients.toString()}
                  left={props => <List.Icon {...props} icon="food-variant" />}
                />
                <Divider />
                <List.Item
                  title="Allergens Detected"
                  description={importStats.allergens.toString()}
                  left={props => <List.Icon {...props} icon="alert-circle" />}
                />
                <Divider />
                <List.Item
                  title="Errors"
                  description={importStats.errors.toString()}
                  left={props => <List.Icon {...props} icon="alert" color={importStats.errors > 0 ? theme.colors.error : theme.colors.text} />}
                />
              </List.Section>

              <Button
                mode="contained"
                icon="check"
                onPress={() => navigation.navigate('FoodList')}
                style={styles.doneButton}
              >
                Done
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.helpCard}>
        <Card.Title title="Supported Databases" />
        <Card.Content>
          <List.Section>
            <List.Subheader>USDA Food Data Central</List.Subheader>
            <List.Item
              title="Foundation Foods"
              description="Nutrient values derived from analyses, aggregated data, and imputed data"
              left={props => <List.Icon {...props} icon="database" />}
            />
            <List.Item
              title="SR Legacy"
              description="Standard Reference foods from the USDA National Nutrient Database"
              left={props => <List.Icon {...props} icon="database" />}
            />
            <List.Item
              title="Survey Foods (FNDDS)"
              description="Foods reported in the What We Eat in America dietary survey"
              left={props => <List.Icon {...props} icon="database" />}
            />
            <List.Item
              title="Branded Foods"
              description="Foods with brand names and nutrition facts labels"
              left={props => <List.Icon {...props} icon="database" />}
            />

            <Divider style={styles.divider} />

            <List.Subheader>Canadian Nutrient File (CNF)</List.Subheader>
            <List.Item
              title="CNF 2015 Dataset"
              description="Canada's standard reference food composition database with bilingual support"
              left={props => <List.Icon {...props} icon="flag" />}
            />

            <Divider style={styles.divider} />

            <List.Subheader>Phenol-Explorer Database</List.Subheader>
            <List.Item
              title="Polyphenol Content Data"
              description="Comprehensive database of polyphenols and antioxidants in foods"
              left={props => <List.Icon {...props} icon="leaf" />}
            />
            <List.Item
              title="Health Benefits Tracking"
              description="Track flavonoids, phenolic acids, stilbenes, and lignans"
              left={props => <List.Icon {...props} icon="heart" />}
            />

            <Divider style={styles.divider} />

            <List.Subheader>FoodB Database</List.Subheader>
            <List.Item
              title="FoodB JSON Dataset"
              description="Comprehensive food composition database with detailed nutrient and compound information"
              left={props => <List.Icon {...props} icon="database" />}
            />
          </List.Section>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    margin: 16,
    elevation: 2,
  },
  helpCard: {
    margin: 16,
    marginTop: 0,
    elevation: 2,
  },
  description: {
    marginBottom: 16,
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  importButton: {
    marginTop: 16,
  },
  websiteButton: {
    marginTop: 8,
  },
  progressContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  progressText: {
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    width: '100%',
    borderRadius: 4,
  },
  spinner: {
    marginTop: 16,
  },
  errorContainer: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
  },
  errorText: {
    fontWeight: 'bold',
  },
  statsContainer: {
    marginTop: 24,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  datasetChip: {
    marginBottom: 16,
  },
  divider: {
    marginVertical: 8,
  },
  doneButton: {
    marginTop: 16,
  },
});

export default FoodImportScreen;
