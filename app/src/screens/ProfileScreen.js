/**
 * Profile Screen for Znü<PERSON>Zähler
 * Displays user profile and settings
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Alert } from 'react-native';
import { Card, Text, Switch, Button, TextInput, Divider, List } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import { getCurrentUser, updateUserSettings, getDatabaseStats, resetDatabase, backupDatabase } from '../services/databaseService';
import SyncManager from '../components/SyncManager';
import LanguageSelector from '../components/LanguageSelector';
import { useTranslation } from '../hooks/useTranslation';

/**
 * Profile Screen Component
 * @returns {JSX.Element} - Profile screen component
 */
const ProfileScreen = () => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const { t, isSwissGerman } = useTranslation();
  const [user, setUser] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState(null);
  const [dbStats, setDbStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showSync, setShowSync] = useState(false);

  // Load user data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Get current user
        const userData = await getCurrentUser();
        setUser(userData);
        setEditedUser(userData);

        // Get database stats
        const stats = await getDatabaseStats();
        setDbStats(stats);
      } catch (error) {
        console.error('Error loading user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle save profile
  const handleSaveProfile = async () => {
    try {
      // Validate required fields
      if (!editedUser.name) {
        Alert.alert(t('common.error'), t('profile.nameRequired'));
        return;
      }

      // Update user settings
      await updateUserSettings(editedUser.id, {
        name: editedUser.name,
        email: editedUser.email,
        preferredLanguage: editedUser.preferred_language,
        measurementUnit: editedUser.measurement_unit
      });

      // Update user state
      setUser(editedUser);

      // Exit edit mode
      setIsEditing(false);

      // Show success message
      Alert.alert(t('common.success'), t('profile.profileUpdated'));
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert(t('common.error'), t('profile.profileUpdateFailed'));
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditedUser(user);
    setIsEditing(false);
  };

  // Handle field change
  const handleChange = (field, value) => {
    setEditedUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle backup database
  const handleBackupDatabase = async () => {
    try {
      const backupPath = await backupDatabase();
      Alert.alert('Success', `Database backed up to: ${backupPath}`);
    } catch (error) {
      console.error('Error backing up database:', error);
      Alert.alert('Error', 'Failed to backup database');
    }
  };

  // Handle reset database
  const handleResetDatabase = () => {
    Alert.alert(
      'Confirm Reset',
      'Are you sure you want to reset the database? This will delete all data and cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await resetDatabase();
              Alert.alert('Success', 'Database reset successfully');

              // Reload page
              const userData = await getCurrentUser();
              setUser(userData);
              setEditedUser(userData);

              const stats = await getDatabaseStats();
              setDbStats(stats);
            } catch (error) {
              console.error('Error resetting database:', error);
              Alert.alert('Error', 'Failed to reset database');
            }
          }
        }
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Card style={styles.card}>
        <Card.Title title={t('profile.title')} />
        <Card.Content>
          {isEditing ? (
            // Edit mode
            <View>
              <TextInput
                label={t('profile.name')}
                value={editedUser.name}
                onChangeText={(text) => handleChange('name', text)}
                style={styles.input}
                mode="outlined"
              />

              <TextInput
                label={t('profile.email')}
                value={editedUser.email}
                onChangeText={(text) => handleChange('email', text)}
                style={styles.input}
                mode="outlined"
                keyboardType="email-address"
              />

              <View style={styles.buttonContainer}>
                <Button onPress={handleCancelEdit} style={styles.button}>
                  {t('common.cancel')}
                </Button>
                <Button mode="contained" onPress={handleSaveProfile} style={styles.button}>
                  {t('common.save')}
                </Button>
              </View>
            </View>
          ) : (
            // View mode
            <View>
              <List.Item
                title={t('profile.name')}
                description={user.name}
                left={props => <List.Icon {...props} icon="account" />}
              />

              <List.Item
                title={t('profile.email')}
                description={user.email || t('profile.notSet')}
                left={props => <List.Icon {...props} icon="email" />}
              />

              <Button
                mode="outlined"
                onPress={() => setIsEditing(true)}
                style={styles.editButton}
              >
                {t('profile.editProfile')}
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Title title={t('profile.preferences')} />
        <Card.Content>
          <LanguageSelector style={styles.languageSelector} />

          <List.Item
            title={t('profile.darkMode')}
            left={props => <List.Icon {...props} icon={isDarkMode ? "weather-night" : "weather-sunny"} />}
            right={() => (
              <Switch
                value={isDarkMode}
                onValueChange={toggleTheme}
              />
            )}
          />
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Title title="Database" />
        <Card.Content>
          {dbStats && (
            <View style={styles.statsContainer}>
              <Text style={styles.statsText}>Version: {dbStats.version}</Text>
              <Text style={styles.statsText}>Tables: {dbStats.tables.length}</Text>
              <Text style={styles.statsText}>Total Records: {dbStats.totalRecords}</Text>
            </View>
          )}

          <View style={styles.buttonRow}>
            <Button
              mode="outlined"
              onPress={handleBackupDatabase}
              style={styles.databaseButton}
            >
              Backup Database
            </Button>

            <Button
              mode="outlined"
              onPress={handleResetDatabase}
              style={[styles.databaseButton, styles.dangerButton]}
            >
              Reset Database
            </Button>
          </View>

          <Button
            mode="outlined"
            icon="database"
            onPress={() => navigation.navigate('FoodDatabase')}
            style={styles.syncButton}
          >
            Food Database
          </Button>

          <Button
            mode="outlined"
            onPress={() => setShowSync(!showSync)}
            style={styles.syncButton}
          >
            {showSync ? 'Hide Sync Options' : 'Show Sync Options'}
          </Button>

          {showSync && <SyncManager />}
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Title title="About" />
        <Card.Content>
          <Text style={styles.aboutText}>
            ZnüniZähler v1.0.0
          </Text>
          <Text style={styles.aboutText}>
            A nutrition tracking app for monitoring your food intake
          </Text>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    margin: 16,
    marginBottom: 8,
    elevation: 2,
  },
  input: {
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    width: '48%',
  },
  editButton: {
    marginTop: 16,
  },
  statsContainer: {
    marginBottom: 16,
  },
  statsText: {
    marginBottom: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  databaseButton: {
    width: '48%',
  },
  dangerButton: {
    borderColor: '#B00020',
    color: '#B00020',
  },
  syncButton: {
    marginTop: 16,
  },
  aboutText: {
    marginBottom: 8,
    textAlign: 'center',
  },
});

export default ProfileScreen;
