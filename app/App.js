import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, Platform, Linking } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import * as Updates from 'expo-updates';
import { MaterialIcons } from '@expo/vector-icons';
import AppNavigator from './src/navigation/AppNavigator';
import ErrorBoundary from './src/components/ErrorBoundary';
import { logError, log, LOG_LEVELS, getDeviceInfo } from './src/services/logService';
import { initCompileTimeLogging } from './src/utils/compileTimeLogger';
import { initRuntimeMonitoring } from './src/utils/runtimeMonitor';

// Global error handler for uncaught JS errors
const setupErrorHandlers = () => {
  try {
    // Handle uncaught JS errors - only if ErrorUtils is available
    if (global.ErrorUtils) {
      const originalErrorHandler = global.ErrorUtils.getGlobalHandler();

      global.ErrorUtils.setGlobalHandler((error, isFatal) => {
        // Log the error
        logError(error, 'GlobalErrorHandler', {
          critical: true,
          isFatal,
        });

        // Call the original handler
        originalErrorHandler(error, isFatal);
      });
    } else {
      console.warn('ErrorUtils not available, skipping global error handler setup');
    }

    // Handle promise rejections
    const handlePromiseRejection = (event) => {
      logError(
        event.reason || new Error('Unhandled Promise Rejection'),
        'PromiseRejectionHandler',
        {
          critical: true,
          message: 'Unhandled Promise Rejection',
        }
      );
    };

    // Add event listener for unhandled promise rejections
    if (global.addEventListener) {
      global.addEventListener('unhandledrejection', handlePromiseRejection);
    }
  } catch (error) {
    console.error('Error setting up error handlers:', error);
  }
};

// App recovery actions
const AppRecoveryActions = ({ onReset }) => {
  const handleRestart = async () => {
    try {
      await log(LOG_LEVELS.INFO, 'Manual app restart requested');

      // Check if we can use Expo Updates to reload
      if (Updates && typeof Updates.reloadAsync === 'function') {
        try {
          await Updates.reloadAsync();
        } catch (updateError) {
          console.warn('Failed to reload with Updates:', updateError);
          onReset();
        }
      } else {
        console.warn('Updates.reloadAsync not available, using fallback');
        // Fallback to reset handler
        onReset();
      }
    } catch (error) {
      console.error('Failed to restart app:', error);
      // Fallback to reset handler
      onReset();
    }
  };

  return (
    <View style={styles.recoveryContainer}>
      <TouchableOpacity
        style={styles.recoveryButton}
        onPress={handleRestart}
      >
        <MaterialIcons name="refresh" size={24} color="#fff" />
        <Text style={styles.recoveryButtonText}>Restart App</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.recoveryButton, styles.supportButton]}
        onPress={() => Linking.openURL('mailto:<EMAIL>?subject=App%20Error%20Report')}
      >
        <MaterialIcons name="email" size={24} color="#fff" />
        <Text style={styles.recoveryButtonText}>Contact Support</Text>
      </TouchableOpacity>
    </View>
  );
};

// Main App component
export default function App() {
  // Set up global error handlers and logging systems
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize logging systems
        initCompileTimeLogging();
        initRuntimeMonitoring();

        // Set up error handlers
        setupErrorHandlers();

        // Log app startup with comprehensive information
        const deviceInfo = await getDeviceInfo();
        await log(LOG_LEVELS.INFO, 'App started with enhanced logging', {
          timestamp: new Date().toISOString(),
          deviceInfo,
          loggingFeatures: {
            compileTimeLogging: true,
            runtimeMonitoring: true,
            enhancedErrorBoundary: true,
            performanceTracking: true,
          },
          appVersion: require('./package.json').version || 'unknown',
          buildType: __DEV__ ? 'development' : 'production',
        });

        // Log successful initialization
        await log(LOG_LEVELS.INFO, 'Logging infrastructure initialized successfully', {
          features: [
            'Compile-time error capture',
            'Runtime performance monitoring',
            'Enhanced error boundaries',
            'Comprehensive log export',
            'Memory usage tracking',
            'Network request monitoring',
            'Security event logging',
          ],
        });

      } catch (error) {
        console.error('Failed to initialize app logging:', error);
        // Fallback to basic error logging
        logError(error, 'App Initialization', {
          critical: true,
          fallbackMode: true,
        });
      }
    };

    initializeApp();
  }, []);

  return (
    <ErrorBoundary
      componentName="App"
      showDetails={true}
      showReportButton={true}
      onReport={() => Linking.openURL('mailto:<EMAIL>?subject=App%20Error%20Report')}
      fallback={<AppRecoveryActions onReset={() => { }} />}
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <AppNavigator />
          <StatusBar style="auto" />
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  recoveryContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  recoveryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    marginVertical: 10,
    width: '80%',
    maxWidth: 300,
  },
  supportButton: {
    backgroundColor: '#2196F3',
  },
  recoveryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});
