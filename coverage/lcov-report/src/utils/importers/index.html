
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils/importers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/utils/importers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.05% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>45/638</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">2.63% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>6/228</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.12% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>8/66</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.23% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>44/608</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="CNFImporter.js"><a href="CNFImporter.js.html">CNFImporter.js</a></td>
	<td data-value="34.09" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 34%"></div><div class="cover-empty" style="width: 66%"></div></div>
	</td>
	<td data-value="34.09" class="pct low">34.09%</td>
	<td data-value="132" class="abs low">45/132</td>
	<td data-value="14.63" class="pct low">14.63%</td>
	<td data-value="41" class="abs low">6/41</td>
	<td data-value="47.05" class="pct low">47.05%</td>
	<td data-value="17" class="abs low">8/17</td>
	<td data-value="34.64" class="pct low">34.64%</td>
	<td data-value="127" class="abs low">44/127</td>
	</tr>

<tr>
	<td class="file low" data-value="FoodBImporter.js"><a href="FoodBImporter.js.html">FoodBImporter.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="120" class="abs low">0/120</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="116" class="abs low">0/116</td>
	</tr>

<tr>
	<td class="file low" data-value="PhenolExplorerImporter.js"><a href="PhenolExplorerImporter.js.html">PhenolExplorerImporter.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="140" class="abs low">0/140</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="52" class="abs low">0/52</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="17" class="abs low">0/17</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="134" class="abs low">0/134</td>
	</tr>

<tr>
	<td class="file low" data-value="USDAImporter.js"><a href="USDAImporter.js.html">USDAImporter.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="246" class="abs low">0/246</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="86" class="abs low">0/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="231" class="abs low">0/231</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-06T11:24:58.410Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    