
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/models</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/models</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">41.95% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>253/603</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.54% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>150/309</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52.23% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>35/67</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">42.16% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>245/581</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="Consumption.js"><a href="Consumption.js.html">Consumption.js</a></td>
	<td data-value="67.46" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 67%"></div><div class="cover-empty" style="width: 33%"></div></div>
	</td>
	<td data-value="67.46" class="pct medium">67.46%</td>
	<td data-value="166" class="abs medium">112/166</td>
	<td data-value="68.23" class="pct medium">68.23%</td>
	<td data-value="85" class="abs medium">58/85</td>
	<td data-value="85" class="pct high">85%</td>
	<td data-value="20" class="abs high">17/20</td>
	<td data-value="67.08" class="pct medium">67.08%</td>
	<td data-value="161" class="abs medium">108/161</td>
	</tr>

<tr>
	<td class="file medium" data-value="Food.js"><a href="Food.js.html">Food.js</a></td>
	<td data-value="57.71" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 57%"></div><div class="cover-empty" style="width: 43%"></div></div>
	</td>
	<td data-value="57.71" class="pct medium">57.71%</td>
	<td data-value="149" class="abs medium">86/149</td>
	<td data-value="72.36" class="pct medium">72.36%</td>
	<td data-value="76" class="abs medium">55/76</td>
	<td data-value="70.58" class="pct medium">70.58%</td>
	<td data-value="17" class="abs medium">12/17</td>
	<td data-value="58.04" class="pct medium">58.04%</td>
	<td data-value="143" class="abs medium">83/143</td>
	</tr>

<tr>
	<td class="file low" data-value="Ingredient.js"><a href="Ingredient.js.html">Ingredient.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="78" class="abs low">0/78</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="34" class="abs low">0/34</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	</tr>

<tr>
	<td class="file low" data-value="MealType.js"><a href="MealType.js.html">MealType.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="53" class="abs low">0/53</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="52" class="abs low">0/52</td>
	</tr>

<tr>
	<td class="file low" data-value="Nutrient.js"><a href="Nutrient.js.html">Nutrient.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="64" class="abs low">0/64</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	</tr>

<tr>
	<td class="file medium" data-value="User.js"><a href="User.js.html">User.js</a></td>
	<td data-value="59.13" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 59%"></div><div class="cover-empty" style="width: 41%"></div></div>
	</td>
	<td data-value="59.13" class="pct medium">59.13%</td>
	<td data-value="93" class="abs medium">55/93</td>
	<td data-value="57.81" class="pct medium">57.81%</td>
	<td data-value="64" class="abs medium">37/64</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="8" class="abs medium">6/8</td>
	<td data-value="62.06" class="pct medium">62.06%</td>
	<td data-value="87" class="abs medium">54/87</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-06T11:24:58.410Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    