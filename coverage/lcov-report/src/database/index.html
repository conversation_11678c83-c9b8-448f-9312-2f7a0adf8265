
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/database</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/database</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">40.55% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>161/397</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.86% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>28/97</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45.36% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>44/97</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">40.75% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>152/373</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="DataAccess.js"><a href="DataAccess.js.html">DataAccess.js</a></td>
	<td data-value="69.31" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.31" class="pct medium">69.31%</td>
	<td data-value="189" class="abs medium">131/189</td>
	<td data-value="40.74" class="pct low">40.74%</td>
	<td data-value="54" class="abs low">22/54</td>
	<td data-value="79.41" class="pct medium">79.41%</td>
	<td data-value="34" class="abs medium">27/34</td>
	<td data-value="70.52" class="pct medium">70.52%</td>
	<td data-value="173" class="abs medium">122/173</td>
	</tr>

<tr>
	<td class="file low" data-value="DatabaseManager.js"><a href="DatabaseManager.js.html">DatabaseManager.js</a></td>
	<td data-value="16.04" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.04" class="pct low">16.04%</td>
	<td data-value="187" class="abs low">30/187</td>
	<td data-value="14.63" class="pct low">14.63%</td>
	<td data-value="41" class="abs low">6/41</td>
	<td data-value="28.81" class="pct low">28.81%</td>
	<td data-value="59" class="abs low">17/59</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="180" class="abs low">30/180</td>
	</tr>

<tr>
	<td class="file low" data-value="DatabaseProvider.js"><a href="DatabaseProvider.js.html">DatabaseProvider.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="21" class="abs low">0/21</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-06T11:00:55.078Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    