module.exports = {
  preset: 'jest-expo',
  transformIgnorePatterns: [
    'node_modules/(?!(jest-)?react-native|@react-native|react-clone-referenced-element|@react-native-community|expo(nent)?|@expo(nent)?/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|@sentry/.*)'
  ],
  setupFilesAfterEnv: ['<rootDir>/app/src/tests/setup.js'],
  collectCoverage: true,
  collectCoverageFrom: [
    'app/src/**/*.{js,jsx}',
    '!app/src/tests/**',
    '!**/node_modules/**'
  ],
  coverageReporters: ['text', 'lcov'],
  testMatch: ['**/__tests__/**/*.js?(x)', '**/?(*.)+(spec|test).js?(x)'],
  testPathIgnorePatterns: ['/node_modules/', '/build/', '/app/test.js'],
  moduleFileExtensions: ['js', 'jsx', 'json', 'node'],
  moduleNameMapper: {
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/app/src/tests/__mocks__/fileMock.js',
    '\\.(css|less)$': '<rootDir>/app/src/tests/__mocks__/styleMock.js'
  }
};
